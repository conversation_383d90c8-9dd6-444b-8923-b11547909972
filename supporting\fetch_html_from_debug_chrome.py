import subprocess
import time
import requests
import pyperclip
import json
import sys
import os

def get_clipboard_url():
    return pyperclip.paste().strip()

def launch_chrome(url):
    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
    if not os.path.exists(chrome_path):
        chrome_path = r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
    if not os.path.exists(chrome_path):
        print("Chrome not found. Please adjust the chrome_path variable.")
        sys.exit(1)
    
    # Use a dedicated user data directory to ensure the debugging port is opened,
    # even if another Chrome instance is already running.
    script_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    user_data_dir = os.path.join(script_dir, "chrome_dev_profile")

    subprocess.Popen([
        chrome_path,
        f"--user-data-dir={user_data_dir}",
        "--remote-debugging-port=9222",
        "--new-window",
        url
    ])
    time.sleep(3)  # Give Chrome time to start

def get_chrome_tabs():
    for _ in range(10):
        try:
            resp = requests.get("http://localhost:9222/json")
            return resp.json()
        except Exception as e:
            time.sleep(1)
    print("Could not connect to Chrome remote debugging port at http://localhost:9222. Make sure Chrome is running with --remote-debugging-port=9222.")
    sys.exit(1)

def find_tab_by_url(tabs, url):
    for tab in tabs:
        if tab.get("url") == url:
            return tab
    return None

def wait_for_tab(url, timeout=15):
    for _ in range(timeout):
        tabs = get_chrome_tabs()
        tab = find_tab_by_url(tabs, url)
        if tab:
            return tab
        time.sleep(1)
    print("Tab with the specified URL not found.")
    sys.exit(1)

def fetch_html_from_tab(tab):
    ws_url = tab["webSocketDebuggerUrl"]
    import websocket
    ws = websocket.create_connection(ws_url)
    ws.send(json.dumps({"id": 1, "method": "Page.enable"}))
    ws.recv()
    
    # The Page.loadEventFired is an event, not a method.
    # This was causing an error. We'll just wait for a fixed time.
    time.sleep(2)

    ws.send(json.dumps({
        "id": 2,
        "method": "Runtime.evaluate",
        "params": {
            "expression": "document.documentElement.outerHTML",
            "returnByValue": True
        }
    }))
    while True:
        result = json.loads(ws.recv())
        if result.get("id") == 2:
            if 'error' in result:
                print(f"Error evaluating javascript: {result['error']['message']}")
                ws.close()
                sys.exit(1)
            html = result["result"]["result"]["value"]
            ws.close()
            return html

def main():
    url = get_clipboard_url()
    launch_chrome(url)
    tab = wait_for_tab(url)
    html = fetch_html_from_tab(tab)
    with open("output.html", "w", encoding="utf-8") as f:
        f.write(html)

if __name__ == "__main__":
    main()
