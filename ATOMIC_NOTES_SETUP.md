# Atomic Notes from Reddit Post - Setup Guide

This guide will help you set up and use the `atomic_notes_from_reddit_post.py` script to generate atomic notes from Reddit posts using Google Gemini API.

## Prerequisites

### 1. Install Required Dependencies

Install the required Python packages using pip:

```bash
pip install -r requirements.txt
```

Or install individually:
```bash
pip install pyperclip beautifulsoup4 google-generativeai requests dateparser lxml
```

### 2. Get Google Gemini API Key

1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Copy the API key

### 3. Set Environment Variable

**On Windows (Command Prompt):**
```cmd
set GOOGLE_GEMINI_API_KEY=your-api-key-here
```

**On Windows (PowerShell):**
```powershell
$env:GOOGLE_GEMINI_API_KEY="your-api-key-here"
```

**On Linux/Mac:**
```bash
export GOOGLE_GEMINI_API_KEY='your-api-key-here'
```

For permanent setup, add the export line to your `.bashrc`, `.zshrc`, or equivalent shell configuration file.

## Usage

### Step 1: Get Reddit Post HTML
1. Navigate to a Reddit post in your browser
2. Copy the HTML content to clipboard (using browser developer tools or a bookmarklet)
3. Run the Reddit extraction script:
   ```bash
   python all_messages_from_post_html.py
   ```
   This will process the HTML and copy the formatted Reddit content to your clipboard.

### Step 2: Generate Atomic Notes
Run the atomic notes script:

```bash
# Using default model (gemini-1.5-flash)
python atomic_notes_from_reddit_post.py

# Using a specific model
python atomic_notes_from_reddit_post.py gemini-1.5-pro
```

### Available Models
- `gemini-1.5-flash` (default) - Faster, good for most tasks
- `gemini-1.5-pro` - More capable, better for complex analysis

## How It Works

1. **Loads prompt** from `atomic_notes_from_reddit_post_prompt.txt`
2. **Gets Reddit content** by running `all_messages_from_post_html.py` and reading from clipboard
3. **Calls Gemini API** with the combined prompt and Reddit content
4. **Outputs results** to terminal and copies to clipboard

## Files

- `atomic_notes_from_reddit_post.py` - Main script
- `atomic_notes_from_reddit_post_prompt.txt` - Prompt template
- `all_messages_from_post_html.py` - Reddit HTML extraction script
- `requirements.txt` - Python dependencies

## Troubleshooting

### API Key Issues
- Make sure the environment variable is set correctly
- Verify the API key is valid and has proper permissions

### No Reddit Content
- Ensure you've run `all_messages_from_post_html.py` first
- Check that the Reddit HTML was properly copied to clipboard

### Model Errors
- Try using `gemini-1.5-flash` instead of `gemini-1.5-pro`
- Check Google AI Studio for available models

### Import Errors
- Run `pip install -r requirements.txt` to install all dependencies
- Make sure you're using the correct Python environment

## Help

Run the script with help flag for usage information:
```bash
python atomic_notes_from_reddit_post.py --help
```
