import re
from bs4 import BeautifulSoup
from datetime import datetime, timed<PERSON><PERSON>

def extract_reddit_posts(html_content):
    soup = BeautifulSoup(html_content, 'lxml')
    posts = soup.find_all('shreddit-post')
    
    extracted_posts = []
    
    for post in posts:
        post_title = post.get('post-title')
        post_url_slug = post.get('permalink')
        
        # Extract the base URL without the slug
        post_url_match = re.match(r'(/r/[^/]+/comments/[^/]+/)', post_url_slug)
        if not post_url_match:
            continue
        
        post_url = f"https://www.reddit.com{post_url_match.group(1)}"
        
        created_timestamp_str = post.get('created-timestamp')
        
        # Convert timestamp to datetime object
        # Handle both 'Z' and '+0000' timezone formats
        if created_timestamp_str.endswith('Z'):
            timestamp_str = created_timestamp_str.replace('Z', '+00:00')
        elif created_timestamp_str.endswith('+0000'):
            timestamp_str = created_timestamp_str.replace('+0000', '+00:00')
        else:
            timestamp_str = created_timestamp_str

        utc_time = datetime.fromisoformat(timestamp_str)
        
        # Convert UTC to EDT (UTC-4)
        edt_time = utc_time - timedelta(hours=4)
        
        # Format the time as YYYYMMDDHHmm
        formatted_time = edt_time.strftime('%Y%m%d%H%M')
        
        extracted_posts.append(f"- {formatted_time} - [{post_title}]({post_url})")
        
    return extracted_posts

import pyperclip

if __name__ == "__main__":
    try:
        html_content = pyperclip.paste()
        
        if not html_content:
            print("Clipboard is empty.")
        else:
            posts = extract_reddit_posts(html_content)
            
            output = "\n".join(posts)
            pyperclip.copy(output)
            print(output)
            print("\nResults copied to clipboard.")
            
    except Exception as e:
        print(f"An error occurred: {e}")
