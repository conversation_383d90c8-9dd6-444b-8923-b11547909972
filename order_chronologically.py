#!/usr/bin/env python3
"""
Order Chronologically Script

This script allows you to browse for a plain text file or .md file and will order
any markdown header section containing a list of dated lines in chronological order
from latest date (top) to earliest date (bottom).

Expected format:
- YYYYMMDDHHMM - [Title](URL)
"""

import re
import os
import sys
from pathlib import Path
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
from typing import List, Tuple, Optional
import argparse


class ChronologicalSorter:
    def __init__(self):
        self.date_pattern = re.compile(r'^(\s*-\s*)(\d{12})\s*-\s*(.+)$')
        
    def extract_date_from_line(self, line: str) -> Optional[Tuple[str, str, str]]:
        """
        Extract date and content from a line.
        Returns (prefix, date, content) or None if no date found.
        """
        match = self.date_pattern.match(line.strip())
        if match:
            prefix = match.group(1)  # "- " or similar
            date = match.group(2)    # YYYYMMDDHHMM
            content = match.group(3) # Rest of the line
            return (prefix, date, content)
        return None
    
    def is_markdown_header(self, line: str) -> bool:
        """Check if line is a markdown header (starts with #)"""
        return line.strip().startswith('#')
    
    def process_file_content(self, content: str) -> str:
        """
        Process the entire file content and sort dated lists under headers.
        """
        lines = content.split('\n')
        result_lines = []
        current_section = []
        in_dated_list = False
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # Check if this is a markdown header
            if self.is_markdown_header(line):
                # Process any accumulated section
                if current_section:
                    if in_dated_list:
                        sorted_section = self.sort_dated_section(current_section)
                        result_lines.extend(sorted_section)
                    else:
                        result_lines.extend(current_section)
                    current_section = []
                    in_dated_list = False
                
                # Add the header
                result_lines.append(line)
                i += 1
                continue
            
            # Check if this line has a date pattern
            date_info = self.extract_date_from_line(line)
            if date_info:
                if not in_dated_list:
                    # Starting a new dated list section
                    in_dated_list = True
                current_section.append(line)
            else:
                # Not a dated line
                if in_dated_list:
                    # End of dated list, process it
                    sorted_section = self.sort_dated_section(current_section)
                    result_lines.extend(sorted_section)
                    current_section = []
                    in_dated_list = False
                
                # Add non-dated line directly
                result_lines.append(line)
            
            i += 1
        
        # Process any remaining section
        if current_section:
            if in_dated_list:
                sorted_section = self.sort_dated_section(current_section)
                result_lines.extend(sorted_section)
            else:
                result_lines.extend(current_section)
        
        return '\n'.join(result_lines)
    
    def sort_dated_section(self, lines: List[str]) -> List[str]:
        """
        Sort a section of dated lines chronologically (latest first).
        """
        dated_lines = []
        
        for line in lines:
            date_info = self.extract_date_from_line(line)
            if date_info:
                prefix, date, content = date_info
                dated_lines.append((date, prefix, content, line))
        
        # Sort by date in descending order (latest first)
        dated_lines.sort(key=lambda x: x[0], reverse=True)
        
        # Return the sorted lines
        return [item[3] for item in dated_lines]


class ChronologicalSorterGUI:
    def __init__(self):
        self.sorter = ChronologicalSorter()
        self.root = tk.Tk()
        self.root.title("Chronological Order Tool")
        self.root.geometry("800x600")
        
        self.setup_ui()
        
    def setup_ui(self):
        # File selection frame
        file_frame = tk.Frame(self.root)
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(file_frame, text="Select file:").pack(side=tk.LEFT)
        
        self.file_path_var = tk.StringVar()
        file_entry = tk.Entry(file_frame, textvariable=self.file_path_var, state='readonly')
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        
        browse_btn = tk.Button(file_frame, text="Browse", command=self.browse_file)
        browse_btn.pack(side=tk.RIGHT)
        
        # Buttons frame
        btn_frame = tk.Frame(self.root)
        btn_frame.pack(fill=tk.X, padx=10, pady=5)
        
        process_btn = tk.Button(btn_frame, text="Process File", command=self.process_file)
        process_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        save_btn = tk.Button(btn_frame, text="Save Result", command=self.save_result)
        save_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Text area for results
        text_frame = tk.Frame(self.root)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        tk.Label(text_frame, text="Result:").pack(anchor=tk.W)
        
        self.result_text = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD)
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.processed_content = ""
        
    def browse_file(self):
        file_path = filedialog.askopenfilename(
            title="Select file to process",
            filetypes=[
                ("Text files", "*.txt"),
                ("Markdown files", "*.md"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.file_path_var.set(file_path)
    
    def process_file(self):
        file_path = self.file_path_var.get()
        if not file_path:
            messagebox.showerror("Error", "Please select a file first.")
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.processed_content = self.sorter.process_file_content(content)
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(1.0, self.processed_content)
            
            messagebox.showinfo("Success", "File processed successfully!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error processing file: {str(e)}")
    
    def save_result(self):
        if not self.processed_content:
            messagebox.showerror("Error", "No processed content to save.")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="Save processed file",
            defaultextension=".md",
            filetypes=[
                ("Markdown files", "*.md"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.processed_content)
                messagebox.showinfo("Success", f"File saved to: {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Error saving file: {str(e)}")
    
    def run(self):
        self.root.mainloop()


def main():
    parser = argparse.ArgumentParser(description="Order chronologically dated markdown lists")
    parser.add_argument("--file", "-f", help="Input file path")
    parser.add_argument("--output", "-o", help="Output file path")
    parser.add_argument("--gui", action="store_true", help="Launch GUI mode")
    
    args = parser.parse_args()
    
    if args.gui or (not args.file):
        # Launch GUI
        app = ChronologicalSorterGUI()
        app.run()
    else:
        # Command line mode
        sorter = ChronologicalSorter()
        
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            processed_content = sorter.process_file_content(content)
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(processed_content)
                print(f"Processed file saved to: {args.output}")
            else:
                print(processed_content)
                
        except Exception as e:
            print(f"Error: {str(e)}", file=sys.stderr)
            sys.exit(1)


if __name__ == "__main__":
    main()
