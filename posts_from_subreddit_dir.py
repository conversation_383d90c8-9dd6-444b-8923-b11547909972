import re
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import time

def extract_reddit_posts(html_content):
    """Extract Reddit posts from HTML content with improved parsing."""
    soup = BeautifulSoup(html_content, 'lxml')

    # Try multiple selectors for different Reddit layouts
    posts = []

    # New Reddit layout
    shreddit_posts = soup.find_all('shreddit-post')
    posts.extend(shreddit_posts)

    # Alternative selectors for different Reddit versions
    if not posts:
        # Try other common Reddit post selectors
        alt_posts = soup.find_all('div', {'data-testid': 'post-container'})
        posts.extend(alt_posts)

    if not posts:
        # Try even more generic selectors
        alt_posts = soup.find_all('article')
        posts.extend(alt_posts)

    extracted_posts = []

    print(f"Found {len(posts)} potential posts in HTML")

    for i, post in enumerate(posts):
        try:
            # Try to extract from shreddit-post attributes
            if post.name == 'shreddit-post':
                post_title = post.get('post-title')
                post_url_slug = post.get('permalink')
                created_timestamp_str = post.get('created-timestamp')
            else:
                # Try to extract from other post formats
                # Look for title in various ways
                title_elem = (post.find('h3') or
                             post.find('a', {'data-testid': 'post-title'}) or
                             post.find('a', class_=re.compile(r'.*title.*', re.I)))

                post_title = title_elem.get_text(strip=True) if title_elem else None

                # Look for permalink
                permalink_elem = post.find('a', href=re.compile(r'/r/.*/comments/'))
                post_url_slug = permalink_elem.get('href') if permalink_elem else None

                # Look for timestamp
                time_elem = post.find('time') or post.find('[datetime]')
                created_timestamp_str = time_elem.get('datetime') if time_elem else None

            if not post_title or not post_url_slug:
                continue

            # Extract the base URL without the slug
            post_url_match = re.match(r'(/r/[^/]+/comments/[^/]+/)', post_url_slug)
            if not post_url_match:
                continue

            post_url = f"https://www.reddit.com{post_url_match.group(1)}"

            if created_timestamp_str:
                try:
                    # Handle different timestamp formats
                    if 'T' in created_timestamp_str:
                        # ISO format
                        utc_time = datetime.fromisoformat(created_timestamp_str.replace('Z', '+00:00'))
                    else:
                        # Try parsing as timestamp
                        utc_time = datetime.fromtimestamp(float(created_timestamp_str))

                    # Convert UTC to EDT (UTC-4)
                    edt_time = utc_time - timedelta(hours=4)

                    # Format the time as YYYYMMDDHHmm
                    formatted_time = edt_time.strftime('%Y%m%d%H%M')
                except (ValueError, TypeError):
                    # If timestamp parsing fails, use current time
                    formatted_time = datetime.now().strftime('%Y%m%d%H%M')
            else:
                formatted_time = datetime.now().strftime('%Y%m%d%H%M')

            extracted_posts.append(f"- {formatted_time} - [{post_title}]({post_url})")

        except Exception as e:
            print(f"Error processing post {i}: {e}")
            continue

    return extracted_posts

import pyperclip
import os

def save_debug_html(html_content, filename="debug_reddit.html"):
    """Save HTML content to file for debugging."""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"Debug HTML saved to {filename}")
    except Exception as e:
        print(f"Could not save debug HTML: {e}")

if __name__ == "__main__":
    try:
        html_content = pyperclip.paste()

        if not html_content:
            print("Clipboard is empty.")
        else:
            print(f"HTML content length: {len(html_content)} characters")

            # Save debug HTML
            save_debug_html(html_content)

            # Check for Reddit-specific elements
            if 'shreddit-post' in html_content:
                print("✓ Found shreddit-post elements")
            else:
                print("✗ No shreddit-post elements found")

            if 'reddit.com' in html_content:
                print("✓ Content appears to be from Reddit")
            else:
                print("✗ Content doesn't appear to be from Reddit")

            posts = extract_reddit_posts(html_content)

            if posts:
                output = "\n".join(posts)
                pyperclip.copy(output)
                print(f"\n✓ Successfully extracted {len(posts)} posts:")
                print(output)
                print("\nResults copied to clipboard.")
            else:
                print("\n✗ No posts were extracted. Check the debug HTML file.")

    except Exception as e:
        print(f"An error occurred: {e}")
        import traceback
        traceback.print_exc()
