import requests
import sys
import pyperclip

def fetch_html(url):
    """
    Fetches the HTML content of a URL and saves it to a file.

    Args:
        url (str): The URL to fetch.
    """
    try:
        # Set a common user-agent to avoid being blocked
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise an exception for bad status codes

        # Save the HTML content to a file
        with open('output.html', 'w', encoding='utf-8') as f:
            f.write(response.text)

        print(f"Successfully fetched and saved HTML from {url} to output.html")

    except requests.exceptions.RequestException as e:
        print(f"Error fetching URL: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Get URL from clipboard
    url_to_fetch = pyperclip.paste()

    if not url_to_fetch.startswith('http'):
        print("Clipboard does not contain a valid URL.")
        sys.exit(1)

    fetch_html(url_to_fetch)
