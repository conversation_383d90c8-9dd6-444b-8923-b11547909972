import re
from bs4 import BeautifulSoup
from datetime import datetime, timezone, timedelta

# Alternative: Simple EDT/EST conversion without pytz dependency
def convert_to_edt_simple(utc_time_str):
    """
    Simple conversion to Eastern Time (EDT/EST) without external dependencies.
    Uses EDT (UTC-4) during daylight saving time, EST (UTC-5) otherwise.
    """
    try:
        # Handle different timestamp formats
        if utc_time_str.endswith('Z'):
            utc_time_str = utc_time_str.replace('Z', '+00:00')
        elif utc_time_str.endswith('+0000'):
            utc_time_str = utc_time_str.replace('+0000', '+00:00')
        elif '+0000' in utc_time_str:
            utc_time_str = utc_time_str.replace('+0000', '+00:00')

        # Parse UTC time
        utc_time = datetime.fromisoformat(utc_time_str)

        # Simple daylight saving time check (approximate)
        # DST typically runs from 2nd Sunday in March to 1st Sunday in November
        year = utc_time.year
        march_second_sunday = datetime(year, 3, 8) + timedelta(days=(6 - datetime(year, 3, 8).weekday()) % 7)
        november_first_sunday = datetime(year, 11, 1) + timedelta(days=(6 - datetime(year, 11, 1).weekday()) % 7)

        # Check if date falls within DST period
        if march_second_sunday <= utc_time.replace(tzinfo=None) < november_first_sunday:
            # EDT (UTC-4)
            offset_hours = 4
        else:
            # EST (UTC-5)
            offset_hours = 5

        # Convert to Eastern Time
        eastern_time = utc_time - timedelta(hours=offset_hours)
        return eastern_time.strftime('%Y%m%d%H%M')

    except Exception as e:
        print(f"Error converting timestamp '{utc_time_str}': {e}")
        return ""

# Advanced version with pytz (requires: pip install pytz)
try:
    import pytz
    PYTZ_AVAILABLE = True
except ImportError:
    PYTZ_AVAILABLE = False

def convert_to_local_time(utc_time_str, target_timezone='US/Eastern'):
    """
    Converts a UTC timestamp string to a formatted local time string.
    Uses pytz if available, otherwise falls back to simple Eastern Time conversion.

    Args:
        utc_time_str: UTC timestamp string (e.g., '2024-07-05T15:52:00.000Z')
        target_timezone: Target timezone (default: 'US/Eastern')
                        Other examples: 'US/Pacific', 'US/Central', 'US/Mountain', 'UTC'

    Returns:
        Formatted time string in YYYYMMDDHHMM format
    """
    if not PYTZ_AVAILABLE or target_timezone == 'US/Eastern':
        # Use simple conversion for Eastern Time or when pytz is not available
        return convert_to_edt_simple(utc_time_str)

    try:
        # Handle different timestamp formats
        if utc_time_str.endswith('Z'):
            # Format: 2025-07-04T22:24:07.975000Z
            utc_time_str = utc_time_str.replace('Z', '+00:00')
        elif utc_time_str.endswith('+0000'):
            # Format: 2025-07-04T22:24:07.975000+0000
            utc_time_str = utc_time_str.replace('+0000', '+00:00')
        elif '+0000' in utc_time_str:
            # Format: 2025-07-04T22:24:07+0000 (without microseconds)
            utc_time_str = utc_time_str.replace('+0000', '+00:00')

        # Parse UTC time
        utc_time = datetime.fromisoformat(utc_time_str)

        # Convert to target timezone
        target_tz = pytz.timezone(target_timezone)

        # If the datetime is already timezone-aware, convert directly
        if utc_time.tzinfo is not None:
            local_time = utc_time.astimezone(target_tz)
        else:
            # If naive datetime, assume it's UTC and localize first
            utc_tz = pytz.UTC
            utc_time = utc_tz.localize(utc_time)
            local_time = utc_time.astimezone(target_tz)

        return local_time.strftime('%Y%m%d%H%M')
    except Exception as e:
        print(f"Error converting timestamp '{utc_time_str}': {e}")
        return convert_to_edt_simple(utc_time_str)  # Fallback to simple conversion

def extract_reddit_post_info(html_content):
    soup = BeautifulSoup(html_content, 'lxml')
    
    # --- Extract Post Information ---
    post_element = soup.find('shreddit-post')
    if not post_element:
        return "No post found."

    post_title = post_element.get('post-title', 'No Title')
    post_url_slug = post_element.get('permalink', '')
    post_url_match = re.match(r'(/r/[^/]+/comments/[^/]+/)', post_url_slug)
    post_url = f"https://www.reddit.com{post_url_match.group(1)}" if post_url_match else ""
    
    created_timestamp_str = post_element.get('created-timestamp', '')
    formatted_post_time = convert_to_local_time(created_timestamp_str) if created_timestamp_str else ""

    output = [f"- {formatted_post_time} - [{post_title}]({post_url})"]
        
    return "\n".join(output)

import pyperclip

if __name__ == "__main__":
    # Test the timestamp conversion with the problematic format
    test_timestamp = '2025-07-04T22:24:07.975000+0000'
    print(f"Testing timestamp conversion:")
    print(f"Input: {test_timestamp}")
    print(f"Output: {convert_to_edt_simple(test_timestamp)}")
    print()

    try:
        html_content = pyperclip.paste()

        if not html_content:
            print("Clipboard is empty.")
        else:
            result = extract_reddit_post_info(html_content)
            pyperclip.copy(result)
            print(result)
            print("\nResults copied to clipboard.")

    except Exception as e:
        print(f"An error occurred: {e}")
