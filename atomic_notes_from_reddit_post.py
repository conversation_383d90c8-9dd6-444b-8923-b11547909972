#!/usr/bin/env python3
"""
Simple LLM call script that sends Reddit post content to Google Gemini API
to generate atomic notes based on a dynamically loaded prompt.
"""

import os
import sys
import subprocess
import pyperclip
import google.generativeai as genai
from pathlib import Path


def load_prompt_from_file(prompt_file_path):
    """Load the prompt text from the specified file."""
    try:
        with open(prompt_file_path, 'r', encoding='utf-8') as f:
            return f.read().strip()
    except FileNotFoundError:
        print(f"Error: Prompt file not found at {prompt_file_path}")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading prompt file: {e}")
        sys.exit(1)


def get_reddit_content():
    """Get Reddit content by running the all_messages_from_post_html.py script."""
    try:
        # Run the script and capture its output
        result = subprocess.run(
            [sys.executable, "all_messages_from_post_html.py"],
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        if result.returncode != 0:
            print(f"Error running all_messages_from_post_html.py: {result.stderr}")
            sys.exit(1)
        
        # The script copies content to clipboard, so we get it from there
        reddit_content = pyperclip.paste()
        
        if not reddit_content:
            print("No content found in clipboard after running the Reddit script.")
            sys.exit(1)
            
        return reddit_content
        
    except Exception as e:
        print(f"Error getting Reddit content: {e}")
        sys.exit(1)


def setup_gemini_api(model_name=None):
    """Setup Google Gemini API with API key from environment variable."""
    api_key = os.getenv('GOOGLE_GEMINI_API_KEY')

    if not api_key:
        print("Error: GOOGLE_GEMINI_API_KEY environment variable not set.")
        print("Please set your Google Gemini API key as an environment variable:")
        print("On Windows: set GOOGLE_GEMINI_API_KEY=your-api-key-here")
        print("On Linux/Mac: export GOOGLE_GEMINI_API_KEY='your-api-key-here'")
        sys.exit(1)

    # Default to gemini-2.5-pro if no model specified
    if not model_name:
        model_name = 'gemini-2.5-pro'

    try:
        genai.configure(api_key=api_key)
        print(f"Using model: {model_name}")
        return genai.GenerativeModel(model_name)
    except Exception as e:
        print(f"Error setting up Gemini API: {e}")
        print("Available models typically include: gemini-2.5-pro, gemini-1.5-pro, gemini-1.5-flash")
        sys.exit(1)


def call_gemini_api(model, prompt, reddit_content):
    """Make API call to Google Gemini."""
    try:
        # Combine prompt and content
        full_prompt = f"{prompt}\n\n---\n\n{reddit_content}"
        
        print("Sending request to Gemini API...")
        print(f"Content length: {len(full_prompt)} characters")
        
        # Generate response
        response = model.generate_content(full_prompt)
        
        if not response.text:
            print("Error: Empty response from Gemini API")
            sys.exit(1)
            
        return response.text
        
    except Exception as e:
        print(f"Error calling Gemini API: {e}")
        sys.exit(1)


def main():
    """Main function to orchestrate the LLM call process."""
    print("=== Atomic Notes from Reddit Post ===")

    # Check for model argument
    model_name = None
    if len(sys.argv) > 1:
        model_name = sys.argv[1]
        print(f"Using specified model: {model_name}")

    # Define file paths
    script_dir = Path(__file__).parent
    prompt_file = script_dir / "atomic_notes_from_reddit_post_prompt.txt"

    # Load prompt from file
    print(f"Loading prompt from: {prompt_file}")
    prompt = load_prompt_from_file(prompt_file)
    print(f"Prompt loaded successfully ({len(prompt)} characters)")

    # Get Reddit content
    print("Getting Reddit content...")
    reddit_content = get_reddit_content()
    print(f"Reddit content retrieved ({len(reddit_content)} characters)")

    # Setup Gemini API
    print("Setting up Gemini API...")
    model = setup_gemini_api(model_name)
    print("Gemini API configured successfully")
    
    # Make API call
    print("\n" + "="*50)
    response = call_gemini_api(model, prompt, reddit_content)
    
    # Print response to terminal
    print("RESPONSE:")
    print("="*50)
    print(response)
    print("="*50)
    
    # Copy response to clipboard
    try:
        pyperclip.copy(response)
        print("\nResponse copied to clipboard successfully!")
    except Exception as e:
        print(f"Warning: Could not copy to clipboard: {e}")
    
    print("\nProcess completed successfully!")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("Usage: python atomic_notes_from_reddit_post.py [model_name]")
        print("")
        print("This script generates atomic notes from Reddit post content using Google Gemini API.")
        print("")
        print("Prerequisites:")
        print("1. Set GOOGLE_GEMINI_API_KEY environment variable")
        print("2. Have Reddit post HTML content in clipboard (run all_messages_from_post_html.py first)")
        print("3. Ensure atomic_notes_from_reddit_post_prompt.txt exists in the same directory")
        print("")
        print("Optional arguments:")
        print("  model_name    Gemini model to use (default: gemini-2.5-pro)")
        print("                Common options: gemini-2.5-pro, gemini-1.5-pro, gemini-1.5-flash")
        print("")
        print("Examples:")
        print("  python atomic_notes_from_reddit_post.py")
        print("  python atomic_notes_from_reddit_post.py gemini-1.5-pro")
        sys.exit(0)

    main()
