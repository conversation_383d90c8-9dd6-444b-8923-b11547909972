# This script uses pyperclip and BeautifulSoup to parse HTML from the clipboard.
# Documentation for these libraries was retrieved using context7.
# Pyperclip docs: /asweigart/pyperclip
# BeautifulSoup docs: /wention/beautifulsoup4

import pyperclip
from bs4 import BeautifulSoup
import datetime
import dateparser

def parse_blueprint_html(html_content):
    """
    Parses the HTML content of a Dyson Sphere Program blueprint page to extract details.

    Args:
        html_content: The HTML content from the clipboard.

    Returns:
        A formatted string with the blueprint details, or an error message.
    """
    # Initialize BeautifulSoup to parse the HTML content.
    # See context7 docs for /wention/beautifulsoup4 for more info.
    soup = BeautifulSoup(html_content, 'html.parser')

    # --- Selectors based on dsp_blueprint.html ---

    # Find the blueprint name using a CSS selector.
    name_tag = soup.select_one('div.t-blueprint__title h2')
    name = name_tag.text.strip() if name_tag else "Blueprint Name Not Found"

    # Find the blueprint URL from the 'og:url' meta tag.
    url_tag = soup.select_one('meta[property="og:url"]')
    url = url_tag['content'] if url_tag else "URL Not Found"

    # Find the author's name.
    author_tag = soup.select_one('div.t-blueprint__info a[href*="/users/"]')
    author = author_tag.text.strip() if author_tag else "Author Not Found"

    # Find the creation date.
    date_tag = soup.select_one('span.t-blueprint__info-date')
    date_str = date_tag.text.strip() if date_tag else ""
    
    # Parse the relative date string.
    date_obj = dateparser.parse(date_str)
    timestamp = date_obj.strftime("%Y%m%d%H%M") if date_obj else "Date Not Found"

    # Format the output string.
    output = f"{timestamp} - [{name}]({url}) - {author}"
    
    return output

if __name__ == "__main__":
    try:
        # Get HTML from clipboard using pyperclip.
        # See context7 docs for /asweigart/pyperclip for more info.
        html_from_clipboard = pyperclip.paste()

        if not html_from_clipboard.strip().startswith('<'):
            print("Clipboard does not contain valid HTML.")
        else:
            # Parse the HTML and get the formatted string.
            formatted_string = parse_blueprint_html(html_from_clipboard)
            
            # Copy the result to the clipboard.
            pyperclip.copy(formatted_string)
            print("Blueprint info copied to clipboard:")
            print(formatted_string)

    except Exception as e:
        print(f"An error occurred: {e}")
